"""Service layer for managing notes."""

import uuid
from typing import List, Optional
from uuid import UUID, uuid4

from azure.cosmos.exceptions import CosmosHttpResponseError, CosmosResourceNotFoundError

from peepsapi.crud.models.base import DeleteReason, soft_delete_fields
from peepsapi.crud.models.note import Note, NoteObjectType, NoteVisibility
from peepsapi.crud.utils.constants import NOTE_GROUP_NAMESPACE
from peepsapi.models import now
from peepsapi.services.cosmos_containers import get_notes_container
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.error_handling import (
    ResourceNotFoundError,
    ServerError,
    ValidationError,
)
from peepsapi.utils.logging import get_logger
from peepsapi.utils.markdown import sanitize_markdown

logger = get_logger(__name__)


def gen_note_group_id(
    author_id: UUID,
    object_id: UUID,
) -> UUID:
    """
    Generate a unique note group ID based on the author and object IDs.

    Args:
        author_id (UUID): The ID of the author.
        object_id (UUID): The ID of the object.

    Returns:
        UUID: Note group ID in the format "<author_id>-<object_id>" sorted in ascending order.
    """
    note_group_str = "-".join(sorted([str(author_id), str(object_id)]))
    return uuid.uuid5(NOTE_GROUP_NAMESPACE, note_group_str)


class NotesService:
    """Business logic for notes."""

    def create_note(
        self,
        author_id: UUID,
        object_id: UUID,
        object_type: NoteObjectType,
        content: str,
        tags: Optional[List[str]] = None,
        notes_container: CosmosContainer = get_notes_container(),
    ) -> Note:
        # TODO: Post V1 make this flexible
        if object_type != NoteObjectType.PERSON:
            raise ValidationError(
                message="Only person object type supported",
                error_code="INVALID_OBJECT_TYPE",
            )

        if not content or not content.strip():
            raise ValidationError(
                message="Content is required",
                error_code="MISSING_CONTENT",
            )

        record = Note(
            id=uuid4(),
            author_id=author_id,
            object_id=object_id,
            object_type=object_type,
            note_group_id=gen_note_group_id(author_id, object_id),
            content=sanitize_markdown(content),
            created_at=now(),
            visibility=NoteVisibility.PRIVATE,  # TODO: Post V1 make this configurable
            tags=tags,
        )
        try:
            return notes_container.create_model(record)
        except Exception as e:  # pragma: no cover - Cosmos failures
            logger.error(
                "❌ Failed to create note",
                extra={
                    "error": str(e),
                    "author_id": str(author_id),
                    "object_id": str(object_id),
                    "operation": "create",
                    "status": "error",
                },
            )
            raise ServerError(message="Error creating note")

    def get_notes_for_person(
        self, author_id: UUID, notes_container: CosmosContainer = get_notes_container()
    ) -> List[Note]:
        query = (
            "SELECT * FROM c "
            "WHERE c.deleted = false "
            "ORDER BY (IS_DEFINED(c.updated_at) ? c.updated_at : c.created_at) DESC"
        )
        try:
            return notes_container.query_models(
                query=query,
                model_class=Note,
                partition_key=author_id,
            )
        except Exception as e:  # pragma: no cover - Cosmos failures
            logger.error(
                "❌ Failed fetching notes",
                extra={
                    "error": str(e),
                    "author_id": str(author_id),
                },
            )
            raise ServerError(message="Error fetching notes")

    def get_notes_for_object(
        self,
        author_id: UUID,
        object_id: UUID,
        object_type: NoteObjectType,
        notes_container: CosmosContainer = get_notes_container(),
    ) -> List[Note]:
        query = (
            "SELECT * FROM c "
            "WHERE c.object_id = @oid AND c.deleted = false "
            "ORDER BY (IS_DEFINED(c.updated_at) ? c.updated_at : c.created_at) DESC"
        )
        params = [
            {"name": "@oid", "value": object_id},
        ]
        try:
            return notes_container.query_models(
                query=query,
                parameters=params,
                model_class=Note,
                partition_key=author_id,
            )
        except Exception as e:  # pragma: no cover - Cosmos failures
            logger.error(
                "❌ Failed fetching notes",
                extra={
                    "error": str(e),
                    "author_id": str(author_id),
                    "object_id": str(object_id),
                },
            )
            raise ServerError(message="Error fetching notes")

    def update_note(
        self,
        note_id: UUID,
        author_id: UUID,
        updates: dict,
        notes_container: CosmosContainer = get_notes_container(),
    ) -> Note:
        update_fields = {}
        if "content" in updates and updates["content"] is not None:
            update_fields["content"] = sanitize_markdown(updates["content"])
        if "tags" in updates:
            update_fields["tags"] = updates["tags"]

        if not update_fields:
            raise ValidationError(message="No fields to update")

        update_fields["updated_at"] = now()
        try:
            note: Note = notes_container.patch_model(
                item=note_id,
                partition_key=author_id,
                update_fields=update_fields,
                model_class=Note,
            )
            return note
        except CosmosResourceNotFoundError:
            raise ResourceNotFoundError(message="Note not found or unauthorized")
        except CosmosHttpResponseError as e:
            logger.error(
                "❌ Failed to update note",
                extra={
                    "error": str(e),
                    "note_id": str(note_id),
                    "author_id": str(author_id),
                    "operation": "update",
                    "status": "error",
                },
            )
            raise ServerError(message="Error updating note")

    def delete_note(
        self,
        note_id: UUID,
        author_id: UUID,
        notes_container: CosmosContainer = get_notes_container(),
    ) -> Note:
        try:
            note: Note = notes_container.patch_model(
                item=note_id,
                partition_key=author_id,
                update_fields=soft_delete_fields(author_id, DeleteReason.BY_AUTHOR),
                model_class=Note,
            )
            return note
        except CosmosResourceNotFoundError:
            logger.warning(
                "Note not found for deletion", extra={"note_id": str(note_id)}
            )
            raise ResourceNotFoundError(message="Note not found")
        except Exception as e:  # pragma: no cover
            logger.error(
                "❌ Failed to delete note",
                extra={
                    "error": str(e),
                    "note_id": str(note_id),
                    "author_id": str(author_id),
                    "operation": "delete",
                    "status": "error",
                },
            )
            raise ServerError(message="Error deleting note")
