"""Base models for CRUD

This module contains shared/reusable crud models.
These models represent common data structures like previews, reactions, and media.
"""

from enum import Enum
from typing import Dict, List, Literal, Optional, Tuple
from uuid import UUID

from pydantic import BaseModel, Field

from peepsapi.models import DateTimeModelMixin, UTCDateTime, now
from peepsapi.models.base import BaseModelWithExtra


class DeleteReason(str, Enum):
    """Reason for deleting content."""

    BY_AUTHOR = "by author"
    BY_MODERATOR = "by moderator"


def soft_delete_fields(deleted_by: UUID, reason: DeleteReason) -> dict:
    return {
        "deleted": True,
        "deleted_at": now(),
        "deleted_by": deleted_by,
        "delete_reason": reason,
        "updated_at": now(),
    }


def restore_fields() -> dict:
    return {
        "deleted": False,
        "deleted_at": None,
        "deleted_by": None,
        "delete_reason": None,
        "updated_at": now(),
    }


class DeleteMixin(BaseModelWithExtra):
    """A mixin class that adds delete functionality to content."""

    deleted: bool = False
    deleted_at: Optional[UTCDateTime] = None
    deleted_by: Optional[UUID] = None
    delete_reason: Optional[DeleteReason] = None

    def soft_delete_fields(self, deleted_by: UUID, reason: DeleteReason) -> dict:
        return soft_delete_fields(deleted_by=deleted_by, reason=reason)

    def restore_fields(self) -> dict:
        return restore_fields()


class ModerationFlag(BaseModelWithExtra):
    """A mixin class that adds moderation functionality to content.

    Notes:
        - Each report is stored as a tuple of user ID and the reason.
        - `reports` may contain multiple entries from different users.
    """

    reports: List[Tuple[UUID, Literal["offensive", "boring"]]] = Field(
        default_factory=list
    )


class EventPreview(BaseModelWithExtra):
    """Lightweight embedded preview of an event.

    This model contains just enough information to display an event preview.
    """

    id: UUID
    title: str
    start_time: UTCDateTime
    location: str


class PersonPreview(BaseModelWithExtra):
    """Basic preview of a person's profile, e.g., for authorship or lists.

    This model contains just enough information to display a person preview.
    """

    id: UUID
    name: str
    last_name: str
    current_role: Optional[str]
    current_company: Optional[str]
    location: Optional[str]
    profile_pic_thumb: str


class CommentPreview(BaseModelWithExtra):
    """Lightweight view of a comment for inclusion in posts.

    This model contains just enough information to display a comment preview.
    """

    id: UUID
    author_id: UUID
    content: str
    created_at: str


class SocialLink(DateTimeModelMixin, BaseModelWithExtra):
    """Represents a linked social account or social profile URL.

    This model contains information about a person's social media accounts.
    """

    platform: str  # e.g., 'twitter', 'linkedin'
    url: str  # Full profile URL
    username: Optional[str] = None  # User's handle or username on the platform
    id: Optional[str] = None  # Unique user ID on the platform
    verified: Optional[bool] = False
    connected_at: Optional[UTCDateTime] = None  # ISO timestamp when connected


class Media(BaseModelWithExtra):
    """Attached file reference for a post (image, video, file).

    This model contains information about media attached to a post.
    """

    type: str  # image, video, file
    url: str
    thumbnail_url: Optional[str]


reaction_types = ["like"]
ReactionType = Literal["like"]


class ReactionMixin(BaseModel):
    id: UUID
    reaction_uri: Optional[str] = None
    reaction_type: Optional[ReactionType] = None
    reaction_counts: Dict[str, int] = {}


class PaginationMixin(ModerationFlag, DateTimeModelMixin):
    """
    Base model for paginated data
    """

    id: UUID
    created_at: UTCDateTime
