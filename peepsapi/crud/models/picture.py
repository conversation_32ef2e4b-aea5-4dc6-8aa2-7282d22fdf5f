"""Picture model

Represents all information about picture stored in Azure Blob Storage and it's metadata at upload time.
"""

from typing import Any, Dict, Literal, Optional
from uuid import UUID

from pydantic import Field

from peepsapi.models import UTCDateTime

from .base import BaseStorageModel


class Picture(BaseStorageModel):
    """Representation of a stored picture."""

    parent_type: Literal["person", "post", "comment"]
    parent_id: UUID
    blob_storage_path: str
    size_bytes: int
    width: int
    height: int
    thumbnail_paths: Dict[str, str] = Field(default_factory=dict)
    deleted: bool = False
    version: int = 1
    upload_timestamp: UTCDateTime
    delete_timestamp: Optional[UTCDateTime] = None
    original_filename: Optional[str] = None
    content_type: Optional[str] = None
    format: Optional[str] = None
    mode: Optional[str] = None
    is_animated: bool = False
    exif: Dict[str, Any] = Field(default_factory=dict)
    gps: Optional[Dict[str, Any]] = None
    colors: Optional[list] = None

    # Added by Codex, verify in testing if it can safely be removed
    # model_config = BaseModelWithExtra.model_config.copy()
