"""Person models for the PeepsAPI application.

This module contains models related to people, including the Person model,
<PERSON>ail model, <PERSON>minder model, Achievement model, and Connection model.
"""

from typing import List, Optional
from uuid import UUID

from peepsapi.models import DateTimeModelMixin, UTCDateTime
from peepsapi.models.base import IdentifierType

from .base import BaseStorageModel, EventPreview, SocialLink


class Email(DateTimeModelMixin, BaseStorageModel):
    """Represents an email address record, including historical activation/deactivation.

    This model tracks a person's email address and its activation status.
    """

    type: str
    address: str
    active_since: UTCDateTime  # Using UTCDateTime directly
    deactivated_at: Optional[UTCDateTime] = None  # Using UTCDateTime directly
    verified: bool = False


class PhoneNumber(DateTimeModelMixin, BaseStorageModel):
    """Represents a phone number record, including historical activation/deactivation.

    This model tracks a person's phone number and its activation status.
    """

    type: str  # e.g., "mobile", "work", "home"
    number: str  # E.164 format recommended: +[country code][number]
    active_since: UTCDateTime  # Using UTCDateTime directly
    deactivated_at: Optional[UTCDateTime] = None  # Using UTCDateTime directly
    verified: bool = False


class Reminder(BaseStorageModel):
    """Represents a scheduled reminder relevant to the person.

    This model contains information about reminders associated with a person.
    """

    type: str
    name: str
    start_date: str
    end_date: str
    description: str


class Achievement(BaseStorageModel):
    """Recognitions or achievements associated with a user.

    This model contains information about a person's achievements or recognitions.
    """

    name: str
    color: str  # Hex color code
    comment: str


class Person(DateTimeModelMixin, BaseStorageModel):
    """Represents a person in the system.

    Notes:
        - #TODO reference update when any attribute of person preview is updated
    """

    name: str = ""
    last_name: str = ""
    current_role: str = ""
    current_company: str = ""
    location: str = ""
    profile_pic: Optional[str] = ""
    invited_by_id: Optional[UUID] = None
    member_since: UTCDateTime = None
    social_links: Optional[List[SocialLink]] = None
    emails: List[Email] = []
    phone_numbers: List[PhoneNumber] = []  # New field for structured phone numbers
    upcoming_events: Optional[List[EventPreview]] = None
    reminders: Optional[List[Reminder]] = None
    achievements: Optional[List[Achievement]] = None
    bio: Optional[str] = None  # New field for person's biography
    profile_completed: bool = False  # Whether the user has completed their profile
    # Passkey authentication fields
    has_passkey: bool = False  # Whether the user has registered a passkey
    primary_identifier_type: str = (
        ""  # "email" or "phone" - what they use last to register/recover
    )
    primary_identifier_value: str = (
        ""  # The primary identifier value (email or phone number)
    )
    # Connection status and direction with currently authenticated person
    # (only defined for GET /people/{person_id} endpoint)
    connection_status: Optional[str] = None
    connection_requestee: Optional[bool] = None
    feed_size: int = 0
    remaining_invites: int = 3

    def get_primary_identifier(self) -> str:
        """Get the primary identifier for this person.

        Returns:
            str: The primary identifier (email or phone number)
        """
        if self.primary_identifier_type == IdentifierType.EMAIL and self.emails:
            # Return the first active email address
            for email in self.emails:
                if not email.deactivated_at:
                    return email.address
            # If no active email, return the first email
            return self.emails[0].address
        elif (
            self.primary_identifier_type == IdentifierType.PHONE and self.phone_numbers
        ):
            # Return the first active phone number
            for phone in self.phone_numbers:
                if not phone.deactivated_at:
                    return phone.number
            # If no active phone, return the first phone
            return self.phone_numbers[0].number
        else:
            # Fallback to ID if no email or phone
            return self.id


