"""Note models for the PeepsAPI application.

This module defines the Note model and supporting enums.
"""

from enum import Enum
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel

from peepsapi.models import DateTimeModelMixin, UTCDateTime

from .base import BaseStorageModel, DeleteMixin


class NoteObjectType(str, Enum):
    """Supported object types notes can reference."""

    PERSON = "person"
    COMMUNITY = "community"
    CONVERSATION = "conversation"


class NoteVisibility(str, Enum):
    """Visibility options for a note."""

    PRIVATE = "private"
    SHARED = "shared"
    PUBLIC = "public"
    GROUP = "group"


class NotePayload(BaseModel):
    content: Optional[str] = None
    tags: Optional[List[str]] = None


class Note(DateTimeModelMixin, DeleteMixin, BaseStorageModel):
    """Represents a markdown note authored by a person."""

    author_id: UUID
    object_id: UUID
    object_type: NoteObjectType = NoteObjectType.PERSON
    note_group_id: UUID
    content: str
    created_at: UTCDateTime
    updated_at: Optional[UTCDateTime] = None
    visibility: NoteVisibility = NoteVisibility.PRIVATE
    tags: Optional[List[str]] = None
    version: int = 1
