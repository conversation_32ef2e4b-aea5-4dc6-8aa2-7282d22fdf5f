"""Event models for the PeepsAPI application.

This module contains models related to events, including the Event model and Attendee model.
"""
from typing import List, Literal, Optional
from uuid import UUID

from peepsapi.models import DateTimeModelMixin, UTCDateTime

from .base import BaseStorageModel, CommentPreview, PersonPreview


class Attendee(DateTimeModelMixin, BaseStorageModel):
    """Attendee list for an event.

    This model represents a person's attendance status for an event.
    """

    person: PersonPreview
    status: Literal["declined", "interested", "going", "maybe", "invited"]
    responded_at: UTCDateTime


class Event(DateTimeModelMixin, BaseStorageModel):
    """Represents a community or personal event.

    Notes:
        - Can be hosted by a community or an individual.
        - `community_id` is optional for personal events.
        - Visibility options: public, private, invite-only.
        - RSVP info only reflected in Person/Community for future events.
        - Events support comment threads (via Comment collection).
        - Recurrence support is to be added.
        - #TODO reference update when any attribute of event preview is updated
    """

    title: str
    location: str  # Venue or online link
    created_by: UUID  # Person ID
    start_time: UTCDateTime
    end_time: UTCDateTime
    timezone: str  # e.g., "America/Los_Angeles"
    visibility: Literal["community", "invite-only", "private", "public"]
    created_at: UTCDateTime
    updated_at: Optional[UTCDateTime] = None
    description: Optional[str]
    community_id: Optional[UUID] = None
    comments: Optional[List[CommentPreview]]
    attendees: Optional[List[Attendee]]
    hosts: Optional[List[PersonPreview]]
    tags: Optional[List[str]]
    cover_image_url: Optional[str] = None
