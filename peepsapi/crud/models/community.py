"""Community models for the PeepsAPI application.

This module contains models related to communities, including the Community model,
CommunitySettings model, and Member model.
"""

from typing import List, Literal, Optional
from uuid import UUID

from peepsapi.models import UTCDateTime

from .base import BaseStorageModel, EventPreview, PersonPreview, SocialLink


class CommunitySettings(BaseStorageModel):
    """Flags that configure feature availability for community members.

    This model contains boolean flags that control what features are available to community members.
    """

    allow_member_invites: bool
    allow_member_posts: bool
    allow_member_events: bool
    allow_member_comments: bool
    allow_member_likes: bool
    allow_member_media: bool
    allow_member_tags: bool
    allow_member_visibility: bool


class Member(BaseStorageModel):
    """A person's membership record in a community.

    This model represents a person's membership in a community, including their role and join date.
    """

    person_id: UUID
    role: str
    joined_at: str
    left_at: Optional[str] = None


class Community(BaseStorageModel):
    """Represents a shared group or interest space.

    Notes:
        - Can be public, invite only or private.
        - Includes social presence, media, and permission controls.
        - Events and posts can be scoped to a community.
    """

    name: str
    settings: CommunitySettings
    created_by: PersonPreview
    created_at: UTCDateTime
    visibility: Literal["public", "invite_only", "private"]
    description: Optional[str]
    tags: Optional[List[str]]
    members: Optional[List[Member]]
    updated_at: Optional[UTCDateTime] = None
    profile_picture_url: Optional[str]
    cover_picture_url: Optional[str]
    location: Optional[str]
    website: Optional[str]
    social_links: Optional[List[SocialLink]]
    upcoming_events: Optional[List[EventPreview]]

    # Make member_count a computed property
    @property
    def member_count(self) -> int:
        return len(self.members or [])
