"""API endpoints for note CRUD operations."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException

from peepsapi.auth.services.auth_service import auth_service
from peepsapi.crud.models.note import Note, NoteObjectType, NotePayload
from peepsapi.crud.services.notes_service import NotesService
from peepsapi.services.cosmos_containers import get_notes_container
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/notes", tags=["notes"])
service = NotesService()
logger = get_logger(__name__)


@router.post("/{object_type}/{object_id}", response_model=Note)
@handle_exceptions(error_code_prefix="NOTES")
async def create_note(
    object_type: NoteObjectType,
    object_id: UUID,
    payload: NotePayload,
    person_id: UUID = Depends(auth_service.get_current_person),
    notes_container: CosmosContainer = Depends(get_notes_container),
):
    note = service.create_note(
        author_id=person_id,
        object_id=object_id,
        object_type=object_type,
        content=payload.content,
        tags=payload.tags,
        notes_container=notes_container,
    )
    logger.info(
        "📝 Note created",
        extra={
            "note_id": str(note.id),
            "author_id": str(person_id),
            "object_id": str(object_id),
            "operation": "create",
            "status": "success",
        },
    )
    return note


@router.get("/{object_type}/{object_id}", response_model=List[Note])
@handle_exceptions(error_code_prefix="NOTES")
async def list_notes(
    object_type: NoteObjectType,
    object_id: UUID,
    person_id: UUID = Depends(auth_service.get_current_person),
    notes_container: CosmosContainer = Depends(get_notes_container),
):
    return service.get_notes_for_object(
        author_id=person_id,
        object_id=object_id,
        object_type=object_type,
        notes_container=notes_container,
    )


@router.patch("/{note_id}", response_model=Note)
@handle_exceptions(error_code_prefix="NOTES")
async def update_note(
    note_id: UUID,
    payload: NotePayload,
    person_id: UUID = Depends(auth_service.get_current_person),
    notes_container: CosmosContainer = Depends(get_notes_container),
):
    note = service.update_note(
        note_id=note_id,
        author_id=person_id,
        updates=payload.model_dump(exclude_none=True),
        notes_container=notes_container,
    )
    logger.info(
        "📝 Note updated",
        extra={
            "note_id": str(note_id),
            "author_id": str(person_id),
            "object_id": str(note.object_id),
            "operation": "update",
            "status": "success",
        },
    )
    return note


@router.delete("/{note_id}")
@handle_exceptions(error_code_prefix="NOTES")
async def delete_note(
    note_id: UUID,
    person_id: UUID = Depends(auth_service.get_current_person),
    notes_container: CosmosContainer = Depends(get_notes_container),
):
    note = service.delete_note(
        note_id=note_id, author_id=person_id, notes_container=notes_container
    )
    logger.info(
        "📝 Note deleted",
        extra={
            "note_id": str(note_id),
            "author_id": str(person_id),
            "object_id": str(note.object_id),
            "operation": "delete",
            "status": "success",
        },
    )
    return {"ok": True}
