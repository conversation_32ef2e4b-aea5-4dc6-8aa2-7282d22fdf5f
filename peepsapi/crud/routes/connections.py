"""API endpoints and utility functions for managing connections between people."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException

from peepsapi.auth.services import auth_service
from peepsapi.crud.models.base import PersonPreview
from peepsapi.crud.models.connection import (
    Connection,
    ConnectionRequest,
    ConnectionsResponse,
    ConnectionStatus,
    ConnectionUpdate,
)
from peepsapi.crud.models.person import Person
from peepsapi.crud.routes.people import logger, router
from peepsapi.crud.services.connection_service import (
    gen_connection_id,
    get_connection_person_preview_by_person_id,
    get_connections_page,
    lock_connection,
    unlock_connection,
    update_connection_status,
)
from peepsapi.crud.services.picture_service import build_picture_url_by_id
from peepsapi.jobs import job_utils
from peepsapi.services.cosmos_containers import (
    get_active_jobs_container,
    get_connections_container,
    get_locks_container,
    get_people_container,
)
from peepsapi.services.cosmos_db import CosmosContainer, CosmosContainerAsync
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.logging import get_logger

router = APIRouter(prefix="/connections", tags=["connections"])

logger = get_logger(__name__)


@router.get("/{person_id}/active", response_model=ConnectionsResponse)
@handle_exceptions(error_code_prefix="CONNECTION")
def get_active_connections_by_person(
    person_id: UUID,
    next_page: Optional[str] = None,
    connections_container: CosmosContainer = Depends(get_connections_container),
):
    """
    Get all active connections for a specific person.

    Args:
    - person_id (str): The ID of the person whose active connections are being retrieved.
    - next_page (str): The token for fetching the next page of results.
    - connections_container: The Cosmos DB container dependency for connections.

    Returns:
    - dict: A dictionary containing the list of active connections and the next page token.
    """
    logger.info(
        f"Getting active connections for person: {person_id}",
        extra={"person_id": person_id, "next_page": next_page is not None},
    )

    return get_connections_page(
        person_id,
        "c.status = @status",
        [{"name": "@status", "value": ConnectionStatus.ACCEPTED}],
        next_page,
        connections_container,
    )


@router.get("/requests", response_model=ConnectionsResponse)
def get_connection_requests(
    next_page: Optional[str] = None,
    connections_container: CosmosContainer = Depends(get_connections_container),
    current_person_id: UUID = Depends(auth_service.get_current_person),
):
    """
    Get all active connection requests by the authenticated person.

    Args:
    - next_page (str): The token for fetching the next page of results.
    - connections_container: The Cosmos DB container dependency for connections.

    Returns:
    - dict: A dictionary containing the list of connection requests and the next page token.
    """
    return get_connections_page(
        current_person_id,
        "c.status = @status AND c.requester_person_id = @person_id",
        [
            {"name": "@status", "value": ConnectionStatus.REQUESTED},
            {"name": "@person_id", "value": current_person_id},
        ],
        next_page,
        connections_container,
    )


@router.get("/invitations", response_model=ConnectionsResponse)
def get_connection_invitations(
    next_page: Optional[str] = None,
    connections_container: CosmosContainer = Depends(get_connections_container),
    current_person_id: UUID = Depends(auth_service.get_current_person),
):
    """
    Get all active connection invitations received by the authenticated person.

    Args:
    - next_page (str): The token for fetching the next page of results.
    - connections_container: The Cosmos DB container dependency for connections.

    Returns:
    - dict: A dictionary containing the list of connection invitations and the next page token.
    """
    return get_connections_page(
        current_person_id,
        "c.status = @status AND c.requestee_person_id = @person_id",
        [
            {"name": "@status", "value": ConnectionStatus.REQUESTED},
            {"name": "@person_id", "value": current_person_id},
        ],
        next_page,
        connections_container,
    )


@router.post("/")
async def create_connection(
    connection_request: ConnectionRequest,
    people_container: CosmosContainer = Depends(get_people_container),
    connections_container: CosmosContainer = Depends(get_connections_container),
    locks_container: CosmosContainer = Depends(get_locks_container),
    current_person_id: UUID = Depends(auth_service.get_current_person),
):
    """
    Create a new connection between the authenticated person and another person.

    This endpoint allows an authenticated person (requester) to initiate a connection request
    with another person (requestee). If a connection already exists, it updates
    the status to "requested".

    Args:
    - connection_request (ConnectionRequest): The request containing the ID of the person to connect with.
    - people_container: The Cosmos DB container dependency for people.
    - connections_container: The Cosmos DB container dependency for connections.
    - locks_container: The Cosmos DB container dependency for connection locks.

    Raises:
        HTTPException:
    - 400: If the status transition is invalid.
    - 404: If the requester or requestee person is not found.
    - 423: If the connection is locked.

    Returns:
    - dict: An empty dictionary on success.
    """
    requester_person_id = current_person_id
    requestee_person_id = connection_request.person_id

    # Get conistent connection ID: <person_id1>-<person_id2> in a sorted order
    id = gen_connection_id(requester_person_id, requestee_person_id)

    # Aquire a lock on the connection
    lock = lock_connection(id, locks_container)

    try:
        try:
            # If the connection already exists, handle it as a status change
            await update_connection_status(
                id=id,
                actor_person_id=requester_person_id,
                new_status=ConnectionStatus.REQUESTED,
                locks_container=locks_container,
                connections_container=connections_container,
                people_container=people_container,
                skip_lock=True,
            )  # type: ignore

            return {}
        except HTTPException as e:
            if e.status_code != 404:
                raise e

        # Create the connection for the requester
        connection = Connection(
            id=id,
            requester_person_id=requester_person_id,
            requestee_person_id=requestee_person_id,
            status=ConnectionStatus.REQUESTED,
            person_preview=get_connection_person_preview_by_person_id(
                requestee_person_id, people_container
            ),
            owner_person_id=requester_person_id,
        )

        connections_container.create_model(model=connection, model_class=Connection)

        # Create the connection for the requestee
        connection.owner_person_id = requestee_person_id
        connection.person_preview = get_connection_person_preview_by_person_id(
            requester_person_id, people_container
        )
        connections_container.create_model(model=connection, model_class=Connection)
    finally:
        unlock_connection(lock, locks_container)

    return {}


@router.patch("/{counter_person_id}")
async def update_connection(
    counter_person_id: UUID,
    connection_update: ConnectionUpdate,
    people_container: CosmosContainer = Depends(get_people_container),
    connections_container: CosmosContainer = Depends(get_connections_container),
    locks_container: CosmosContainer = Depends(get_locks_container),
    active_jobs_container: CosmosContainerAsync = Depends(get_active_jobs_container),
    current_person_id: UUID = Depends(auth_service.get_current_person),
):
    """
    Update the status of a connection between the authenticated person and the counter person.

    **Valid status transitions:**

    requested -> accepted, requested -> rejected, and rejected -> requested
    - Allowed only for a requestee
    - `PATCH /connections/{requester_person_id}`

    requested -> rescinded
    - Allowed only for a requester
    - `PATCH /connections/{requestee_person_id}`

    accepted -> removed, removed -> requested, and rescinded -> requested
    - Allowed for either person

    Args:
    - counter_person_id (UUID): The ID of the counter person in the connection
    - connection_update (ConnectionUpdate): The new status to update the connection to
    - people_container: The Cosmos DB container dependency for people.
    - connections_container: The Cosmos DB container dependency for connections.
    - locks_container: The Cosmos DB container dependency for connection locks.

    Raises:
        HTTPException:
    - 400: If the status transition is invalid.
    - 404: If the connection or the person is not found.
    - 423: If the connection is locked.

    Returns:
    - dict: An empty dictionary on success.
    """
    await update_connection_status(
        id=gen_connection_id(current_person_id, counter_person_id),
        actor_person_id=current_person_id,
        new_status=connection_update.status,
        locks_container=locks_container,
        connections_container=connections_container,
        people_container=people_container,
        active_jobs_container=active_jobs_container,
        create_job_method=job_utils.create,
    )

    return {}
