"""API endpoints for community management.

This module provides CRUD operations for communities, including:
- Listing all communities
- Getting a specific community
- Creating a new community
- Updating an existing community
- Deleting a community
"""

from typing import List
from uuid import UUID

from azure.cosmos.exceptions import CosmosResourceNotFoundError
from fastapi import APIRouter, Depends

from peepsapi.crud.models.base import PersonPreview
from peepsapi.crud.models.community import Community
from peepsapi.crud.models.person import Person
from peepsapi.crud.services.picture_service import build_picture_url_by_id
from peepsapi.services.cosmos_containers import get_communities_container
from peepsapi.services.cosmos_db import CosmosContainer
from peepsapi.utils.decorators import handle_exceptions
from peepsapi.utils.error_handling import ResourceNotFoundError, ServerError
from peepsapi.utils.logging import get_logger

# TODO: renable when officially implemented
router = APIRouter(prefix="/communities", tags=["communities"], include_in_schema=False)
logger = get_logger(__name__)


@router.get("/", response_model=List[Community])
@handle_exceptions(error_code_prefix="COMMUNITY")
def list_communities(container=Depends(get_communities_container)):
    """Get all communities.

    Returns:
        List[Community]: A list of all communities
    """

    try:
        communities = list(container.read_all_items())
        logger.info(f"✅ Found {len(communities)} communities")
        return communities
    except Exception as e:
        logger.error("❌ Error listing communities", extra={"error": str(e)})
        raise ServerError(
            message="Error listing communities",
            error_code="LIST_ERROR",
            details={"error": str(e)},
        )


@router.get("/{community_id}", response_model=Community)
@handle_exceptions(error_code_prefix="COMMUNITY")
def get_community(
    community_id: str, communities_container=Depends(get_communities_container)
):
    """Get a specific community by ID.

    Args:
        community_id (str): The ID of the community to retrieve
        communities_container: The Cosmos DB container dependency

    Returns:
        Community: The requested community

    Raises:
        ResourceNotFoundError: If the community is not found
    """
    logger.info(f"🎯 Getting community {community_id}")

    try:
        community = communities_container.read_item(
            item=community_id, partition_key=community_id
        )
        return community
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Community {community_id} not found")
        raise ResourceNotFoundError(
            message="Community not found",
            error_code="COMMUNITY_NOT_FOUND",
        )


@router.post("/", response_model=Community)
@handle_exceptions(error_code_prefix="COMMUNITY")
def create_community(
    community: Community, communities_container=Depends(get_communities_container)
):
    """Create a new community.

    Args:
        community (Community): The community to create
        communities_container: The Cosmos DB container dependency

    Returns:
        Community: The created community
    """
    logger.info(
        f"🎯 Creating community {community.id if community.id else '[auto-generated]'}"
    )

    try:
        result = communities_container.create_model(community)
        logger.info(f"✅ Successfully created community {result['id']}")
        return community
    except Exception as e:
        logger.error("❌ Error creating community", extra={"error": str(e)})
        raise ServerError(
            message="Error creating community",
            error_code="CREATE_ERROR",
            details={"error": str(e)},
        )


@router.put("/{community_id}", response_model=Community)
@handle_exceptions(error_code_prefix="COMMUNITY")
def update_community(
    community_id: str,
    community: Community,
    communities_container=Depends(get_communities_container),
):
    """Update an existing community.

    Args:
        community_id (str): The ID of the community to update
        community (Community): The updated community data
        communities_container: The Cosmos DB container dependency

    Returns:
        Community: The updated community

    Raises:
        ResourceNotFoundError: If the community is not found
    """
    logger.info(f"🎯 Updating community {community_id}")

    try:
        # Check if community exists
        communities_container.read_item(item=community_id, partition_key=community_id)
    except CosmosResourceNotFoundError:
        logger.warning(f"⚠️ Community {community_id} not found for update")
        raise ResourceNotFoundError(
            message="Community not found",
            error_code="COMMUNITY_NOT_FOUND",
        )

    # Update the community
    try:
        communities_container.upsert_model(community)
        logger.info(f"✅ Successfully updated community {community_id}")
        return community
    except Exception as e:
        logger.error(
            f"❌ Error updating community {community_id}", extra={"error": str(e)}
        )
        raise ServerError(
            message="Error updating community",
            error_code="UPDATE_ERROR",
            details={"error": str(e)},
        )


@router.delete("/{community_id}")
@handle_exceptions(error_code_prefix="COMMUNITY")
def delete_community(
    community_id: str, communities_container=Depends(get_communities_container)
):
    """Delete a community.

    Args:
        community_id (str): The ID of the community to delete
        communities_container: The Cosmos DB container dependency

    Returns:
        dict: A confirmation message

    Raises:
        ResourceNotFoundError: If the community is not found
    """
    logger.info(f"🎯 Deleting community {community_id}")

    try:
        communities_container.delete_item(item=community_id, partition_key=community_id)
        logger.info(f"✅ Successfully deleted community {community_id}")
        return {"ok": True}
    except CosmosResourceNotFoundError:
        logger.warning(f"🚨 Community {community_id} not found for deletion")
        raise ResourceNotFoundError(
            message="Community not found",
            error_code="COMMUNITY_NOT_FOUND",
        )


def query_communities(
    person_id: str,  # person ID for filtering communities the person is a member of
    query: str = None,
    communities_container: CosmosContainer = Depends(get_communities_container),
):
    """
    Query communities with optional filtering.

    This function returns communities that match the query string and that the person
    has access to. Currently returns mock data for demonstration purposes.

    Args:
        person_id (str): The ID of the person making the query, used for access control
        query (str, optional): Optional query string to filter communities by name
        communities_container: The Cosmos DB container dependency for communities

    Returns:
        List[Community]: A list of communities matching the query
    """
    # Stub data
    mock_community = Community(
        id="deadbeef-0000-4000-8000-0000cafebabe",
        name="Fake Group",
        description="Fake Group description",
        tags=["product", "tech", "startup"],
        visibility="public",
        members=[
            {
                "person_id": "cafebabe-0000-4000-8000-0000deadbeef",
                "role": "admin",
                "joined_at": "2023-01-01T00:00:00Z",
            },
            {
                "person_id": person_id,
                "role": "member",
                "joined_at": "2023-01-02T00:00:00Z",
            },
        ],
        member_count=2,
        created_by=PersonPreview(
            id=UUID("cafebabe-0000-4000-8000-0000deadbeef"),
            name="John",
            last_name="Fakester",
            current_company="Fake Inc.",
            current_role="Software Engineer",
            location="San Francisco, CA",
            profile_pic_thumb=build_picture_url_by_id(
                parent_class=Person, parent_id="cafebabe-0000-4000-8000-0000deadbeef"
            ),
        ),
        created_at="2023-01-01T00:00:00Z",
        profile_picture_url="",
        cover_picture_url="",
        location="Global",
        website="https://example.com",
        social_links=[],
        upcoming_events=[],
        settings={
            "allow_member_invites": True,
            "allow_member_posts": True,
            "allow_member_events": True,
            "allow_member_comments": True,
            "allow_member_likes": True,
            "allow_member_media": True,
            "allow_member_tags": True,
            "allow_member_visibility": True,
        },
    )

    return dict(confidence_scores=[0.9], items=[mock_community])
