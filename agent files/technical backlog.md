# MVP Backlog
1. <PERSON> infra is not working
2. Images support for posts, and comments.
3. Persisted ai conversations
4. SMS integrations (for invite, recovery and verification)
5. Profile email and phone number additions should check against existing emails and phones numbers.
7. Posts reactions, post comment counters
8. Sanitize posts/comments content before DB update

# Data Models Improvements/Simplifications:
1. All ids should be UUID ✅
2. Base Storage model: this object will expose _ (private) attributes of cosmosdb and provide auto id gen similar to **Person**
3. Connection data model: has 2 UUIDs to represent parties. ✅
   - person preview is duplicate of requestee<PERSON><PERSON> by repeating the id, we should remove the extra if rename the presonPreview to requesteePersonPreview. ✅
   - isn't ownerPersonId always same as requestee_person_id??? ✅
4. Nit: all usage of CosmosContainer should be {repo}\_container unless only one used on the methods ✅
5. cosmos_db.py cosmos_containers.py:
   - Async DB
   - Switch entity cosmos containers to fully async (benchmark: feeds async)
   - Edit the entity routes' (posts, connections, etc) functions and callers to make them async

# Imporovements:
* We should use [cosmos] containers at route level and pass it down to services. fix in challenege, device, and token service.
* decorators: There is a problem somewhere here -- if I make a call with incorrect JSON format, e.g.
    ```json
    {
        "foo": 0,
        "bar": 1, // <-- extra comma is not allowed
    }
    ```
    It will print the error, but the request will just hang and not be finished.
    Repro: Found via swagger, making PUT /people/ID with incorrect JSON and the UI just hang (server was not printing request completion message).
* Override Person Cookie:
    - when x-peeps-id is set, middleware should consume it and override get_current_person value
* DB wrapper:
    - add support for query with partition key
    - patch needs _tag validation
    - deprecate simple wrappers such as read_item
    - replace instances of deprecated methods in codebase to {cmd}_model methods
    - once there is no usage of deprecated method, clean up
    - _initialized: bool = False & _initializing: bool = False are excessive. Cleaner to use state in 1 variable -1, 0 , 1 (not initialized, initializing, initialized).
* Logging:
    - verify and test detailed logs json for full contextual logs
    - configure_logging:
        - when local-log-config.json, it will be created with default config (simple) and default log levvel and format (debug, detailed for debug, info, console for start)
        - when user provides log level and format update local-log-config
        - git ignore local-log-config
* Deploy-infra:
    - should this be moved to scripts? need to valuate github actions for code deployments and see if it can be used for infra too?
    - region (West-us) should be read from param files
    - summary: for all resources & their required secrets: provide a summary of success/failure
* Posts, Comments, Feeds:
    - literals -> enum
    - rename: `get_..._page --> get_..._paginated / _by_page`
    - move pagination and soft-delete logic to root level
    - apply soft-delete logic to notes.
    - use `DeleteMixin`
    - wrap job.create in try-catch
    - add visibility indicator (core, public; in mvp?), `type` (ask, update), `canvas_type`, `canvas_id`.
    - consider a rename for `UserGeneratedContent` and re-think it's scope: `TextContentInput`? Add `updated_by`
    - hide fields not required by API, ie `Person.feed_size`. `etag: Optional[str] = Field(default=None, alias="_etag", exclude=True)`
    - hygene: clear and update docstring comments and logs, remove commented-out code
* Tests:
    - --upgrade "starlette>=0.37.2" to fix test warning: DeprecationWarning: The 'app' shortcut is now deprecated.
* Audit logs:
    - are not persisted in cosmosdb!
    - set up audit logs across all endpoints, can we wrap it in middleware?

# Questions:
1. Where did we implement admin endpoints?
2. Clarify endpoints and payloads design for resources with partition_key != ID:
    - `POST|GET /<partition resource name>/<partition key>/<resource nane>/<item id>`
    - or `POST|GET /<resource name>/<partition_key>/<item id>`?
    - `/people/<author_id>/posts/...` or `/posts/<author_id>/<post_id>`?
    - should it be a combined approach?
        - `/people/<author_id>/posts/...` specifies canvas type in the URI, useful for POST
        - `/posts/<author_id>/...` do not specifies canvas type in the URI, its not required for GET
